import os
import numpy as np
from solution import SVDNet
import torch
import time
import warnings
warnings.filterwarnings('ignore')

# Optimize PyTorch settings for inference
torch.set_num_threads(min(4, os.cpu_count()))
if torch.cuda.is_available():
    torch.backends.cudnn.benchmark = True

# 读取配置文件函数
def read_cfg_file(file_path):
    with open(file_path, 'r') as file:
        lines = file.readlines()
        line_fmt = [line.rstrip('\n').split(' ') for line in lines]
    info = line_fmt
    samp_num = int(info[0][0])
    M = int(info[1][0])
    N = int(info[2][0])
    IQ = int(info[3][0])
    R = int(info[4][0])
    return samp_num, M, N, IQ, R
 
if __name__ == "__main__":
    print("<<< Welcome to 2025 Wireless Algorithm Contest! >>>\n")
    ## 不同轮次的输入数据可放在不同文件夹中便于管理，这里用户可以自定义
    PathSet = {0: "./DebugData", 1: "./CompetitionData1", 2: "./CompetitionData2", 3: "./CompetitionData3"}
    PrefixSet = {0: "Round0", 1: "Round1", 2: "Round2", 3: "Round3"}
 
    Ridx = 1  # 设置比赛轮次索引，指明数据存放目录。0:Test; 1: 1st round; 2: 2nd round ...
    PathRaw = PathSet[Ridx]
    Prefix = PrefixSet[Ridx]
   
    # 查找文件夹中包含的所有比赛/测试数据文件，非本轮次数据请不要放在目标文件夹中
    files = os.listdir(PathRaw)
    Caseidxes = []
    for f in sorted(files):
        if f.find('CfgData') != -1 and f.endswith('.txt'):
            Caseidxes.append(f.split('CfgData')[-1].split('.txt')[0])
 
    ## 创建对象并处理
    for Caseidx in Caseidxes:
        print('Processing Round ' + str(Ridx) + ' Case ' + str(Caseidx))
       
        # 读取配置文件 RoundYCfgDataX.txt
        cfg_path = PathRaw + '/' + Prefix + 'CfgData' + Caseidx + '.txt'
        _, M, N, IQ, R = read_cfg_file(cfg_path)
 
        # 读取信道输入文件 RoundYTestDataX.npy
        H_data_file = PathRaw + '/' + Prefix + 'TestData' + Caseidx + '.npy'
        H_data_all = np.load(H_data_file)
        if (M, N, IQ) != H_data_all.shape[1:4]:
            raise ValueError("Channel data loading error!")
 
        # 优化的模型推理
        samp_num = H_data_all.shape[0]
        U_out_all = np.zeros((samp_num, M, R, IQ), dtype=np.float32)
        S_out_all = np.zeros((samp_num, R), dtype=np.float32)
        V_out_all = np.zeros((samp_num, N, R, IQ), dtype=np.float32)

        # 选择最佳设备
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f'Using device: {device}')

        # 创建优化的模型
        model = SVDNet(dim=M, rank=R).to(device)

        # 尝试加载最佳模型权重
        weight_files = ['best_svdnet_optimized.pth', 'best_svdnet.pth', 'svdnet.pth']
        model_loaded = False
        for weight_file in weight_files:
            if os.path.exists(weight_file):
                try:
                    state_dict = torch.load(weight_file, map_location=device)
                    model.load_state_dict(state_dict, strict=False)
                    print(f'Loaded model weights from {weight_file}')
                    model_loaded = True
                    break
                except Exception as e:
                    print(f'Failed to load {weight_file}: {e}')
                    continue

        if not model_loaded:
            print('Warning: No pre-trained weights loaded, using random initialization')

        # 设置模型为评估模式并优化
        model.eval()
        if device == 'cuda':
            model = model.half()  # Use FP16 for faster inference

        # 批量处理以提高效率
        batch_size = min(32, samp_num)  # 动态批量大小

        print(f'Processing {samp_num} samples in batches of {batch_size}...')
        start_time = time.time()

        with torch.no_grad():
            for start_idx in range(0, samp_num, batch_size):
                end_idx = min(start_idx + batch_size, samp_num)
                batch_size_actual = end_idx - start_idx

                # 准备批量数据
                H_batch = torch.from_numpy(H_data_all[start_idx:end_idx]).float()
                if device == 'cuda':
                    H_batch = H_batch.half().to(device, non_blocking=True)
                else:
                    H_batch = H_batch.to(device)

                # 批量推理
                if device == 'cuda':
                    with torch.cuda.amp.autocast():
                        U_batch, S_batch, V_batch = model(H_batch)
                else:
                    U_batch, S_batch, V_batch = model(H_batch)

                # 转换回CPU并存储结果
                if batch_size_actual == 1:
                    # 单样本情况，添加批次维度
                    U_batch = U_batch.unsqueeze(0)
                    S_batch = S_batch.unsqueeze(0)
                    V_batch = V_batch.unsqueeze(0)

                U_out_all[start_idx:end_idx] = U_batch.float().cpu().numpy()
                S_out_all[start_idx:end_idx] = S_batch.float().cpu().numpy()
                V_out_all[start_idx:end_idx] = V_batch.float().cpu().numpy()

        processing_time = time.time() - start_time
        print(f'Processing completed in {processing_time:.2f} seconds ({samp_num/processing_time:.1f} samples/sec)')
        # 确保数据类型正确
        U_out_all = U_out_all.astype(np.float32)
        S_out_all = S_out_all.astype(np.float32)
        V_out_all = V_out_all.astype(np.float32)

        # 验证输出质量
        print(f'Output validation:')
        print(f'  U shape: {U_out_all.shape}, range: [{U_out_all.min():.4f}, {U_out_all.max():.4f}]')
        print(f'  S shape: {S_out_all.shape}, range: [{S_out_all.min():.4f}, {S_out_all.max():.4f}]')
        print(f'  V shape: {V_out_all.shape}, range: [{V_out_all.min():.4f}, {V_out_all.max():.4f}]')

        # 检查奇异值是否按降序排列
        s_sorted_correctly = np.all(np.diff(S_out_all, axis=1) <= 1e-6)
        print(f'  Singular values sorted correctly: {s_sorted_correctly}')

        # 保存输出结果
        TestOutput_file = Prefix + 'TestOutput' + Caseidx + '.npz'
        np.savez_compressed(TestOutput_file, U_out=U_out_all, S_out=S_out_all, V_out=V_out_all)
        print(f"Results saved to {TestOutput_file}")

        # 清理GPU内存
        if device == 'cuda':
            torch.cuda.empty_cache()