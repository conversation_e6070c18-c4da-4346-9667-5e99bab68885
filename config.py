"""
Configuration file for SVDNet training and inference.
Contains all hyperparameters and settings for optimal performance.
"""

import torch
import os

class Config:
    """Configuration class for SVDNet training and inference."""
    
    # Model Architecture
    MODEL = {
        'dim': 64,  # Will be set from config file
        'rank': 32,  # Will be set from config file
        'd_model': 512,
        'transformer_layers': 8,
        'transformer_heads': 16,
        'transformer_dim_feedforward': 2048,
        'dropout': 0.1,
        'use_cbam': True,
        'cbam_reduction': 16,
    }
    
    # Training Configuration
    TRAINING = {
        'epochs': 150,
        'batch_size': 8,  # Reduced for larger model
        'learning_rate': 2e-3,
        'weight_decay': 1e-4,
        'gradient_clip_val': 1.0,
        'warmup_epochs': 5,
        'patience': 15,
        'val_split': 0.1,
        'seed': 42,
        'use_mixed_precision': True,
        'use_adaptive_weighting': True,
    }
    
    # Data Augmentation
    AUGMENTATION = {
        'enabled': True,
        'probability': 0.9,
        'awgn_snr_range': (10, 30),
        'phase_noise_std': 0.1,
        'max_doppler_hz': 100,
        'sample_rate': 1e6,
    }
    
    # Loss Function Weights
    LOSS_WEIGHTS = {
        'reconstruction': 1.0,
        'spectral': 0.1,
        'orthogonality_u': 0.01,
        'orthogonality_v': 0.01,
    }
    
    # Optimization
    OPTIMIZER = {
        'type': 'AdamW',
        'betas': (0.9, 0.999),
        'eps': 1e-8,
        'amsgrad': False,
    }
    
    # Learning Rate Scheduler
    SCHEDULER = {
        'type': 'OneCycleLR',
        'pct_start': 0.1,
        'anneal_strategy': 'cos',
        'div_factor': 25.0,
        'final_div_factor': 1e4,
    }
    
    # Data Loading
    DATA = {
        'num_workers': 4,
        'pin_memory': True,
        'persistent_workers': True,
        'prefetch_factor': 2,
        'drop_last': True,
    }
    
    # Device Configuration
    DEVICE = {
        'use_cuda': torch.cuda.is_available(),
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'num_threads': min(8, os.cpu_count()),
        'benchmark': True,
        'deterministic': False,
    }
    
    # Orthogonality Constraints
    ORTHOGONALITY = {
        'tolerance': 1e-5,
        'max_iterations': 3,
        'use_multi_stage': True,
        'use_verification': True,
        'correction_threshold': 1e-4,
    }
    
    # Model Saving
    CHECKPOINTS = {
        'save_best': True,
        'save_every_n_epochs': 10,
        'save_final': True,
        'best_model_path': 'best_svdnet_optimized.pth',
        'final_model_path': 'svdnet.pth',
        'checkpoint_dir': 'checkpoints',
    }
    
    # Inference Configuration
    INFERENCE = {
        'batch_size': 32,
        'use_fp16': True,
        'use_torch_compile': False,  # Set to True for PyTorch 2.0+
        'optimize_for_inference': True,
    }
    
    # Logging and Monitoring
    LOGGING = {
        'log_every_n_steps': 100,
        'save_training_history': True,
        'history_file': 'training_history.json',
        'verbose': True,
    }
    
    # Advanced Features
    ADVANCED = {
        'use_gradient_checkpointing': False,  # For memory efficiency
        'use_ema': False,  # Exponential Moving Average
        'ema_decay': 0.999,
        'use_label_smoothing': False,
        'label_smoothing_factor': 0.1,
    }
    
    @classmethod
    def update_from_args(cls, args):
        """Update configuration from command line arguments."""
        if hasattr(args, 'batch_size'):
            cls.TRAINING['batch_size'] = args.batch_size
        if hasattr(args, 'epochs'):
            cls.TRAINING['epochs'] = args.epochs
        if hasattr(args, 'lr'):
            cls.TRAINING['learning_rate'] = args.lr
        if hasattr(args, 'device'):
            cls.DEVICE['device'] = args.device
        if hasattr(args, 'num_workers'):
            cls.DATA['num_workers'] = args.num_workers
    
    @classmethod
    def update_model_dims(cls, M, N, R):
        """Update model dimensions from config file."""
        cls.MODEL['dim'] = M
        cls.MODEL['rank'] = R
    
    @classmethod
    def get_device(cls):
        """Get the configured device."""
        return cls.DEVICE['device']
    
    @classmethod
    def setup_torch(cls):
        """Setup PyTorch with optimal settings."""
        torch.set_num_threads(cls.DEVICE['num_threads'])
        if cls.DEVICE['use_cuda'] and torch.cuda.is_available():
            torch.backends.cudnn.benchmark = cls.DEVICE['benchmark']
            torch.backends.cudnn.deterministic = cls.DEVICE['deterministic']
    
    @classmethod
    def print_config(cls):
        """Print current configuration."""
        print("=" * 60)
        print("SVDNet Configuration")
        print("=" * 60)
        
        sections = [
            ('Model', cls.MODEL),
            ('Training', cls.TRAINING),
            ('Augmentation', cls.AUGMENTATION),
            ('Device', cls.DEVICE),
            ('Orthogonality', cls.ORTHOGONALITY),
        ]
        
        for section_name, section_config in sections:
            print(f"\n{section_name}:")
            for key, value in section_config.items():
                print(f"  {key}: {value}")
        
        print("=" * 60)

# Global configuration instance
config = Config()

# Convenience functions
def get_config():
    """Get the global configuration instance."""
    return config

def update_config_from_file(config_file):
    """Update configuration from a JSON file."""
    import json
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            file_config = json.load(f)
        
        # Update configuration sections
        for section_name, section_config in file_config.items():
            if hasattr(config, section_name):
                getattr(config, section_name).update(section_config)

def save_config_to_file(config_file):
    """Save current configuration to a JSON file."""
    import json
    config_dict = {
        'MODEL': config.MODEL,
        'TRAINING': config.TRAINING,
        'AUGMENTATION': config.AUGMENTATION,
        'LOSS_WEIGHTS': config.LOSS_WEIGHTS,
        'OPTIMIZER': config.OPTIMIZER,
        'SCHEDULER': config.SCHEDULER,
        'DATA': config.DATA,
        'DEVICE': config.DEVICE,
        'ORTHOGONALITY': config.ORTHOGONALITY,
        'CHECKPOINTS': config.CHECKPOINTS,
        'INFERENCE': config.INFERENCE,
        'LOGGING': config.LOGGING,
        'ADVANCED': config.ADVANCED,
    }
    
    with open(config_file, 'w') as f:
        json.dump(config_dict, f, indent=2)

if __name__ == "__main__":
    # Print configuration when run as script
    config.print_config()
