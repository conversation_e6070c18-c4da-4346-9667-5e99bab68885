#!/usr/bin/env python3
"""
Test script for Windows compatibility fixes.
This script tests DataLoader creation without actually running training.
"""

import os
import numpy as np
import torch
from torch.utils.data import Dataset
from windows_compat import (
    is_windows, 
    get_safe_num_workers, 
    create_safe_dataloader,
    WindowsSafeDataLoader,
    print_platform_info,
    configure_torch_for_windows
)

class DummyDataset(Dataset):
    """Dummy dataset for testing DataLoader creation."""
    
    def __init__(self, size=100, dim=64):
        self.size = size
        self.dim = dim
        
    def __len__(self):
        return self.size
    
    def __getitem__(self, idx):
        # Create dummy channel matrix data
        H_in = np.random.randn(self.dim, self.dim, 2).astype(np.float32)
        H_gt = np.random.randn(self.dim, self.dim, 2).astype(np.float32)
        return torch.from_numpy(H_in), torch.from_numpy(H_gt)

def test_basic_compatibility():
    """Test basic Windows compatibility functions."""
    print("Testing basic compatibility functions...")
    
    print(f"Is Windows: {is_windows()}")
    print(f"Safe workers for 4: {get_safe_num_workers(4)}")
    print(f"Safe workers for 0: {get_safe_num_workers(0)}")
    print(f"Safe workers (forced single): {get_safe_num_workers(4, force_single_thread=True)}")
    
    print("✓ Basic compatibility tests passed")

def test_dataloader_creation():
    """Test DataLoader creation with Windows-safe settings."""
    print("\nTesting DataLoader creation...")
    
    # Create dummy dataset
    dataset = DummyDataset(size=50, dim=32)
    
    try:
        # Test create_safe_dataloader
        loader1 = create_safe_dataloader(
            dataset, 
            batch_size=8, 
            num_workers=4,
            shuffle=True
        )
        print(f"✓ create_safe_dataloader: batch_size={loader1.batch_size}, "
              f"num_workers={loader1.num_workers}, shuffle={loader1.sampler is not None}")
        
        # Test WindowsSafeDataLoader.create_train_loader
        loader2 = WindowsSafeDataLoader.create_train_loader(
            dataset,
            batch_size=16,
            num_workers=2
        )
        print(f"✓ WindowsSafeDataLoader.create_train_loader: batch_size={loader2.batch_size}, "
              f"num_workers={loader2.num_workers}")
        
        # Test WindowsSafeDataLoader.create_val_loader
        loader3 = WindowsSafeDataLoader.create_val_loader(
            dataset,
            batch_size=32,
            num_workers=1
        )
        print(f"✓ WindowsSafeDataLoader.create_val_loader: batch_size={loader3.batch_size}, "
              f"num_workers={loader3.num_workers}")
        
        print("✓ DataLoader creation tests passed")
        
    except Exception as e:
        print(f"✗ DataLoader creation failed: {e}")
        return False
    
    return True

def test_dataloader_iteration():
    """Test that DataLoaders can actually iterate without errors."""
    print("\nTesting DataLoader iteration...")
    
    dataset = DummyDataset(size=20, dim=16)
    
    try:
        # Create a safe DataLoader
        loader = create_safe_dataloader(
            dataset,
            batch_size=4,
            num_workers=2,  # Will be adjusted to 0 on Windows
            shuffle=False
        )
        
        # Try to iterate through a few batches
        batch_count = 0
        for H_in, H_gt in loader:
            batch_count += 1
            
            # Verify batch shapes
            expected_batch_size = min(4, len(dataset) - (batch_count - 1) * 4)
            assert H_in.shape[0] == expected_batch_size, f"Expected batch size {expected_batch_size}, got {H_in.shape[0]}"
            assert H_in.shape == (expected_batch_size, 16, 16, 2), f"Unexpected H_in shape: {H_in.shape}"
            assert H_gt.shape == (expected_batch_size, 16, 16, 2), f"Unexpected H_gt shape: {H_gt.shape}"
            
            # Only test a few batches
            if batch_count >= 3:
                break
        
        print(f"✓ Successfully iterated through {batch_count} batches")
        print("✓ DataLoader iteration tests passed")
        
    except Exception as e:
        print(f"✗ DataLoader iteration failed: {e}")
        return False
    
    return True

def test_torch_configuration():
    """Test PyTorch configuration for Windows."""
    print("\nTesting PyTorch configuration...")
    
    try:
        # Configure torch for Windows
        configure_torch_for_windows()
        
        # Check thread count
        num_threads = torch.get_num_threads()
        print(f"✓ PyTorch threads configured: {num_threads}")
        
        # Check CUDA settings if available
        if torch.cuda.is_available():
            print(f"✓ CUDA available: benchmark={torch.backends.cudnn.benchmark}, "
                  f"deterministic={torch.backends.cudnn.deterministic}")
        else:
            print("✓ CUDA not available (CPU-only configuration)")
        
        print("✓ PyTorch configuration tests passed")
        
    except Exception as e:
        print(f"✗ PyTorch configuration failed: {e}")
        return False
    
    return True

def test_multiprocessing_safety():
    """Test multiprocessing safety measures."""
    print("\nTesting multiprocessing safety...")
    
    try:
        # This should not raise an error even if called multiple times
        from windows_compat import setup_multiprocessing
        setup_multiprocessing()
        setup_multiprocessing()  # Should handle being called twice
        
        print("✓ Multiprocessing setup completed safely")
        
        # Test that we can create multiple DataLoaders without issues
        dataset = DummyDataset(size=10, dim=8)
        
        loaders = []
        for i in range(3):
            loader = create_safe_dataloader(
                dataset,
                batch_size=2,
                num_workers=1,
                shuffle=(i % 2 == 0)
            )
            loaders.append(loader)
        
        print(f"✓ Created {len(loaders)} DataLoaders successfully")
        print("✓ Multiprocessing safety tests passed")
        
    except Exception as e:
        print(f"✗ Multiprocessing safety test failed: {e}")
        return False
    
    return True

def main():
    """Run all Windows compatibility tests."""
    print("Windows Compatibility Test Suite")
    print("=" * 50)
    
    # Print platform information
    print_platform_info()
    
    # Run all tests
    tests = [
        test_basic_compatibility,
        test_torch_configuration,
        test_multiprocessing_safety,
        test_dataloader_creation,
        test_dataloader_iteration,
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test_func.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All Windows compatibility tests passed!")
        print("The training scripts should now work without multiprocessing errors.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
