#!/usr/bin/env python3
"""
Performance analysis and optimization script for SVDNet.
Analyzes model performance, identifies bottlenecks, and provides optimization suggestions.
"""

import os
import time
import numpy as np
import torch
import torch.nn as nn
from torch.profiler import profile, record_function, ProfilerActivity
from solution import SVDNet
from config import get_config
import json
import matplotlib.pyplot as plt
import seaborn as sns

class PerformanceAnalyzer:
    """Comprehensive performance analysis for SVDNet."""
    
    def __init__(self, model_path="svdnet.pth", device="auto"):
        self.config = get_config()
        self.device = device if device != "auto" else self.config.get_device()
        self.model_path = model_path
        self.model = None
        self.results = {}
        
    def load_model(self, M=64, R=32):
        """Load the SVDNet model."""
        self.model = SVDNet(dim=M, rank=R, weight_path="")
        
        if os.path.exists(self.model_path):
            try:
                state_dict = torch.load(self.model_path, map_location=self.device)
                self.model.load_state_dict(state_dict, strict=False)
                print(f"Model loaded from {self.model_path}")
            except Exception as e:
                print(f"Failed to load model: {e}")
        
        self.model = self.model.to(self.device)
        self.model.eval()
        
    def analyze_model_complexity(self):
        """Analyze model complexity and parameter count."""
        if self.model is None:
            raise ValueError("Model not loaded")
        
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        # Analyze by module
        module_params = {}
        for name, module in self.model.named_modules():
            if len(list(module.children())) == 0:  # Leaf modules
                params = sum(p.numel() for p in module.parameters())
                if params > 0:
                    module_params[name] = params
        
        # Calculate model size in MB
        param_size = total_params * 4 / (1024 * 1024)  # Assuming float32
        
        complexity_results = {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_size_mb': param_size,
            'module_parameters': module_params
        }
        
        self.results['complexity'] = complexity_results
        
        print(f"Model Complexity Analysis:")
        print(f"  Total parameters: {total_params:,}")
        print(f"  Trainable parameters: {trainable_params:,}")
        print(f"  Model size: {param_size:.2f} MB")
        
        return complexity_results
    
    def benchmark_inference_speed(self, batch_sizes=[1, 4, 8, 16, 32], num_runs=100):
        """Benchmark inference speed across different batch sizes."""
        if self.model is None:
            raise ValueError("Model not loaded")
        
        M, R = self.config.MODEL['dim'], self.config.MODEL['rank']
        speed_results = {}
        
        print("Benchmarking inference speed...")
        
        for batch_size in batch_sizes:
            print(f"  Testing batch size {batch_size}...")
            
            # Create dummy input
            dummy_input = torch.randn(batch_size, M, M, 2).to(self.device)
            
            # Warmup
            with torch.no_grad():
                for _ in range(10):
                    _ = self.model(dummy_input)
            
            # Benchmark
            torch.cuda.synchronize() if self.device == 'cuda' else None
            start_time = time.time()
            
            with torch.no_grad():
                for _ in range(num_runs):
                    _ = self.model(dummy_input)
            
            torch.cuda.synchronize() if self.device == 'cuda' else None
            end_time = time.time()
            
            total_time = end_time - start_time
            avg_time_per_batch = total_time / num_runs
            samples_per_second = batch_size / avg_time_per_batch
            
            speed_results[batch_size] = {
                'avg_time_per_batch': avg_time_per_batch,
                'samples_per_second': samples_per_second,
                'total_time': total_time
            }
            
            print(f"    Avg time per batch: {avg_time_per_batch*1000:.2f} ms")
            print(f"    Samples per second: {samples_per_second:.1f}")
        
        self.results['inference_speed'] = speed_results
        return speed_results
    
    def profile_model_execution(self, batch_size=8, num_steps=10):
        """Profile model execution to identify bottlenecks."""
        if self.model is None:
            raise ValueError("Model not loaded")
        
        M = self.config.MODEL['dim']
        dummy_input = torch.randn(batch_size, M, M, 2).to(self.device)
        
        print("Profiling model execution...")
        
        with profile(
            activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA] if self.device == 'cuda' else [ProfilerActivity.CPU],
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        ) as prof:
            with record_function("model_inference"):
                with torch.no_grad():
                    for _ in range(num_steps):
                        _ = self.model(dummy_input)
        
        # Save profiling results
        prof.export_chrome_trace("svdnet_profile.json")
        
        # Analyze key metrics
        key_averages = prof.key_averages().table(sort_by="cuda_time_total" if self.device == 'cuda' else "cpu_time_total", row_limit=20)
        
        profile_results = {
            'key_averages': key_averages,
            'total_time': sum([item.cuda_time_total if self.device == 'cuda' else item.cpu_time_total for item in prof.key_averages()]),
        }
        
        self.results['profiling'] = profile_results
        
        print("Top 10 operations by time:")
        print(key_averages)
        
        return profile_results
    
    def analyze_memory_usage(self, batch_size=8):
        """Analyze memory usage during inference."""
        if self.model is None:
            raise ValueError("Model not loaded")
        
        M = self.config.MODEL['dim']
        dummy_input = torch.randn(batch_size, M, M, 2).to(self.device)
        
        if self.device == 'cuda':
            torch.cuda.reset_peak_memory_stats()
            torch.cuda.empty_cache()
            
            initial_memory = torch.cuda.memory_allocated()
            
            with torch.no_grad():
                output = self.model(dummy_input)
            
            peak_memory = torch.cuda.max_memory_allocated()
            final_memory = torch.cuda.memory_allocated()
            
            memory_results = {
                'initial_memory_mb': initial_memory / (1024 * 1024),
                'peak_memory_mb': peak_memory / (1024 * 1024),
                'final_memory_mb': final_memory / (1024 * 1024),
                'memory_increase_mb': (peak_memory - initial_memory) / (1024 * 1024)
            }
            
            print(f"Memory Usage Analysis:")
            print(f"  Initial memory: {memory_results['initial_memory_mb']:.2f} MB")
            print(f"  Peak memory: {memory_results['peak_memory_mb']:.2f} MB")
            print(f"  Memory increase: {memory_results['memory_increase_mb']:.2f} MB")
            
        else:
            memory_results = {'note': 'Memory analysis only available on CUDA'}
            print("Memory analysis only available on CUDA devices")
        
        self.results['memory'] = memory_results
        return memory_results
    
    def analyze_orthogonality_quality(self, num_samples=100):
        """Analyze the quality of orthogonality constraints."""
        if self.model is None:
            raise ValueError("Model not loaded")
        
        M, R = self.config.MODEL['dim'], self.config.MODEL['rank']
        
        orthogonality_errors_U = []
        orthogonality_errors_V = []
        
        print("Analyzing orthogonality quality...")
        
        with torch.no_grad():
            for _ in range(num_samples):
                dummy_input = torch.randn(1, M, M, 2).to(self.device)
                U, S, V = self.model(dummy_input)
                
                # Convert to complex
                U_complex = torch.complex(U[..., 0], U[..., 1])
                V_complex = torch.complex(V[..., 0], V[..., 1])
                
                # Compute orthogonality errors
                U_gram = torch.matmul(torch.conj(U_complex).transpose(-2, -1), U_complex)
                V_gram = torch.matmul(torch.conj(V_complex).transpose(-2, -1), V_complex)
                
                eye = torch.eye(R, device=self.device, dtype=U_complex.dtype)
                
                U_error = torch.max(torch.abs(U_gram - eye)).item()
                V_error = torch.max(torch.abs(V_gram - eye)).item()
                
                orthogonality_errors_U.append(U_error)
                orthogonality_errors_V.append(V_error)
        
        orthogonality_results = {
            'U_max_error': max(orthogonality_errors_U),
            'U_mean_error': np.mean(orthogonality_errors_U),
            'U_std_error': np.std(orthogonality_errors_U),
            'V_max_error': max(orthogonality_errors_V),
            'V_mean_error': np.mean(orthogonality_errors_V),
            'V_std_error': np.std(orthogonality_errors_V),
            'all_U_errors': orthogonality_errors_U,
            'all_V_errors': orthogonality_errors_V
        }
        
        self.results['orthogonality'] = orthogonality_results
        
        print(f"Orthogonality Quality Analysis:")
        print(f"  U matrix - Max error: {orthogonality_results['U_max_error']:.2e}")
        print(f"  U matrix - Mean error: {orthogonality_results['U_mean_error']:.2e}")
        print(f"  V matrix - Max error: {orthogonality_results['V_max_error']:.2e}")
        print(f"  V matrix - Mean error: {orthogonality_results['V_mean_error']:.2e}")
        
        return orthogonality_results
    
    def generate_optimization_recommendations(self):
        """Generate optimization recommendations based on analysis results."""
        recommendations = []
        
        if 'complexity' in self.results:
            complexity = self.results['complexity']
            if complexity['total_parameters'] > 10_000_000:
                recommendations.append("Consider model pruning to reduce parameter count")
            if complexity['model_size_mb'] > 100:
                recommendations.append("Consider quantization to reduce model size")
        
        if 'inference_speed' in self.results:
            speed = self.results['inference_speed']
            best_throughput = max(speed.values(), key=lambda x: x['samples_per_second'])
            recommendations.append(f"Optimal batch size for throughput: {[k for k, v in speed.items() if v == best_throughput][0]}")
        
        if 'orthogonality' in self.results:
            orth = self.results['orthogonality']
            if orth['U_max_error'] > 1e-3 or orth['V_max_error'] > 1e-3:
                recommendations.append("Orthogonality constraints may need strengthening")
        
        if 'memory' in self.results and 'memory_increase_mb' in self.results['memory']:
            memory = self.results['memory']
            if memory['memory_increase_mb'] > 1000:
                recommendations.append("Consider gradient checkpointing to reduce memory usage")
        
        self.results['recommendations'] = recommendations
        
        print("\nOptimization Recommendations:")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
        
        return recommendations
    
    def save_results(self, filename="performance_analysis.json"):
        """Save analysis results to file."""
        # Convert numpy arrays to lists for JSON serialization
        results_copy = self.results.copy()
        if 'orthogonality' in results_copy:
            orth = results_copy['orthogonality']
            orth['all_U_errors'] = [float(x) for x in orth['all_U_errors']]
            orth['all_V_errors'] = [float(x) for x in orth['all_V_errors']]
        
        with open(filename, 'w') as f:
            json.dump(results_copy, f, indent=2, default=str)
        
        print(f"Analysis results saved to {filename}")
    
    def run_full_analysis(self):
        """Run complete performance analysis."""
        print("Starting comprehensive performance analysis...")
        
        # Load model
        self.load_model()
        
        # Run all analyses
        self.analyze_model_complexity()
        self.benchmark_inference_speed()
        self.analyze_memory_usage()
        self.analyze_orthogonality_quality()
        self.profile_model_execution()
        self.generate_optimization_recommendations()
        
        # Save results
        self.save_results()
        
        print("\nPerformance analysis completed!")

def main():
    """Main function for performance analysis."""
    import argparse
    
    parser = argparse.ArgumentParser(description="SVDNet Performance Analysis")
    parser.add_argument("--model_path", type=str, default="svdnet.pth", help="Path to model weights")
    parser.add_argument("--device", type=str, default="auto", help="Device to use (auto/cpu/cuda)")
    parser.add_argument("--output", type=str, default="performance_analysis.json", help="Output file for results")
    args = parser.parse_args()
    
    analyzer = PerformanceAnalyzer(model_path=args.model_path, device=args.device)
    analyzer.run_full_analysis()
    
    if args.output != "performance_analysis.json":
        analyzer.save_results(args.output)

if __name__ == "__main__":
    main()
