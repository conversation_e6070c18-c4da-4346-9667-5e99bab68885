#!/usr/bin/env python3
"""
Optimized training script for SVDNet with advanced features.
This script implements the most advanced training pipeline for maximum performance.
"""

import os
import sys
import glob
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from solution import SVDNet
import argparse
from tqdm import tqdm
import random
import math
import time
import json
from torch.optim.lr_scheduler import OneCycleLR
from torch.cuda.amp import GradScaler, autocast
import warnings
warnings.filterwarnings('ignore')

def setup_training_environment(seed=42):
    """Setup optimal training environment."""
    # Set random seeds
    torch.manual_seed(seed)
    np.random.seed(seed)
    random.seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        # Optimize CUDA settings
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
    
    # Set optimal number of threads
    torch.set_num_threads(min(8, os.cpu_count()))

def create_optimized_model(dim, rank, device):
    """Create and optimize model for training."""
    model = SVDNet(dim=dim, rank=rank, weight_path="")
    model = model.to(device)
    
    # Enable mixed precision training if available
    if device == 'cuda':
        model = model.half()  # Use FP16 for memory efficiency
    
    return model

def get_optimized_dataloaders(data_files, label_files, batch_size, val_split=0.1, num_workers=4):
    """Create optimized data loaders."""
    from train import EnhancedChannelDataset
    
    # Create dataset with aggressive augmentation
    full_dataset = EnhancedChannelDataset(
        data_files, label_files, 
        augmentation=True,
        augmentation_prob=0.9  # Very aggressive augmentation
    )
    
    # Split dataset
    dataset_size = len(full_dataset)
    val_size = int(val_split * dataset_size)
    train_size = dataset_size - val_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(
        full_dataset, [train_size, val_size]
    )
    
    # Disable augmentation for validation
    val_dataset.dataset.augmentation = False
    
    # Optimized data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        persistent_workers=True,
        prefetch_factor=2,
        drop_last=True  # For stable batch norm
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size * 2,
        shuffle=False,
        num_workers=num_workers // 2,
        pin_memory=True
    )
    
    return train_loader, val_loader

def train_with_mixed_precision(model, train_loader, val_loader, device, epochs=100, lr=1e-3):
    """Advanced training with mixed precision and all optimizations."""
    
    # Advanced optimizer
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=lr,
        weight_decay=1e-4,
        betas=(0.9, 0.999),
        eps=1e-8
    )
    
    # Advanced scheduler
    total_steps = len(train_loader) * epochs
    scheduler = OneCycleLR(
        optimizer,
        max_lr=lr,
        total_steps=total_steps,
        pct_start=0.1,
        anneal_strategy='cos',
        div_factor=25.0,
        final_div_factor=1e4
    )
    
    # Mixed precision scaler
    scaler = GradScaler() if device == 'cuda' else None
    
    # Training metrics
    best_val_loss = float('inf')
    patience = 15
    patience_counter = 0
    
    # Training history
    history = {
        'train_loss': [],
        'val_loss': [],
        'learning_rate': []
    }
    
    print(f"Starting optimized training for {epochs} epochs...")
    
    for epoch in range(1, epochs + 1):
        # Training phase
        model.train()
        train_loss = 0.0
        train_steps = 0
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch}/{epochs}")
        
        for batch_idx, (H_in, H_gt) in enumerate(pbar):
            H_in = H_in.to(device, non_blocking=True)
            H_gt = H_gt.to(device, non_blocking=True)
            
            optimizer.zero_grad()
            
            # Mixed precision forward pass
            if scaler is not None:
                with autocast():
                    U, S, V = model(H_in)
                    loss = compute_optimized_loss(U, S, V, H_gt, device)
                
                scaler.scale(loss).backward()
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                scaler.step(optimizer)
                scaler.update()
            else:
                U, S, V = model(H_in)
                loss = compute_optimized_loss(U, S, V, H_gt, device)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()
            
            scheduler.step()
            
            train_loss += loss.item()
            train_steps += 1
            
            # Update progress bar
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'lr': f'{scheduler.get_last_lr()[0]:.2e}'
            })
        
        avg_train_loss = train_loss / train_steps
        
        # Validation phase
        val_loss = validate_optimized(model, val_loader, device, scaler)
        
        # Update history
        history['train_loss'].append(avg_train_loss)
        history['val_loss'].append(val_loss)
        history['learning_rate'].append(scheduler.get_last_lr()[0])
        
        print(f"Epoch {epoch}: Train Loss = {avg_train_loss:.6f}, Val Loss = {val_loss:.6f}")
        
        # Early stopping and model saving
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), "best_svdnet_optimized.pth")
            print(f"New best model saved with validation loss: {val_loss:.6f}")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch}")
                break
        
        # Save checkpoint every 10 epochs
        if epoch % 10 == 0:
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'best_val_loss': best_val_loss,
                'history': history
            }
            torch.save(checkpoint, f"checkpoint_epoch_{epoch}.pth")
    
    # Save training history
    with open('training_history.json', 'w') as f:
        json.dump(history, f, indent=2)
    
    return model

def compute_optimized_loss(U, S, V, H_gt, device):
    """Compute optimized loss with all enhancements."""
    from train import compute_enhanced_losses
    
    recon_loss, spectral_loss, orth_loss_U, orth_loss_V = compute_enhanced_losses(
        U, S, V, H_gt, device
    )
    
    # Optimized loss weighting
    total_loss = (
        recon_loss + 
        0.1 * spectral_loss + 
        0.01 * (orth_loss_U + orth_loss_V)
    )
    
    return total_loss

def validate_optimized(model, val_loader, device, scaler=None):
    """Optimized validation function."""
    model.eval()
    total_loss = 0.0
    num_batches = 0
    
    with torch.no_grad():
        for H_in, H_gt in val_loader:
            H_in = H_in.to(device, non_blocking=True)
            H_gt = H_gt.to(device, non_blocking=True)
            
            if scaler is not None:
                with autocast():
                    U, S, V = model(H_in)
                    loss = compute_optimized_loss(U, S, V, H_gt, device)
            else:
                U, S, V = model(H_in)
                loss = compute_optimized_loss(U, S, V, H_gt, device)
            
            total_loss += loss.item()
            num_batches += 1
    
    return total_loss / num_batches

def main():
    parser = argparse.ArgumentParser(description="Optimized SVDNet Training")
    parser.add_argument("--data_dir", type=str, default="CompetitionData1")
    parser.add_argument("--batch_size", type=int, default=8)  # Smaller for larger model
    parser.add_argument("--epochs", type=int, default=150)
    parser.add_argument("--lr", type=float, default=2e-3)
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu")
    parser.add_argument("--num_workers", type=int, default=4)
    args = parser.parse_args()
    
    # Setup environment
    setup_training_environment()
    
    # Load configuration
    from train import read_cfg_file
    data_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainData*.npy")))
    label_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainLabel*.npy")))
    cfg_file = os.path.join(args.data_dir, "Round1CfgData1.txt")
    
    if not data_files or not label_files:
        raise FileNotFoundError("Training files not found")
    
    _, M, N, IQ, R = read_cfg_file(cfg_file)
    print(f"Configuration: M={M}, N={N}, R={R}, Device={args.device}")
    
    # Create optimized model
    model = create_optimized_model(M, R, args.device)
    
    # Create optimized data loaders
    train_loader, val_loader = get_optimized_dataloaders(
        data_files, label_files, args.batch_size, num_workers=args.num_workers
    )
    
    print(f"Training samples: {len(train_loader.dataset)}")
    print(f"Validation samples: {len(val_loader.dataset)}")
    
    # Start optimized training
    start_time = time.time()
    trained_model = train_with_mixed_precision(
        model, train_loader, val_loader, args.device, args.epochs, args.lr
    )
    training_time = time.time() - start_time
    
    print(f"Training completed in {training_time:.2f} seconds")
    
    # Save final model
    torch.save(trained_model.state_dict(), "svdnet.pth")
    print("Final model saved as svdnet.pth")

if __name__ == "__main__":
    main()
