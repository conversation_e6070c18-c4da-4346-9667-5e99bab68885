"""
Windows compatibility utilities for PyTorch DataLoader and multiprocessing.
This module provides utilities to handle Windows-specific issues with PyTorch training.
"""

import os
import platform
import torch
from torch.utils.data import DataLoader
import warnings

def is_windows():
    """Check if running on Windows."""
    return platform.system() == 'Windows' or os.name == 'nt'

def get_safe_num_workers(requested_workers=4, force_single_thread=None):
    """
    Get safe number of workers for DataLoader on current platform.
    
    Args:
        requested_workers (int): Desired number of workers
        force_single_thread (bool): Force single-threaded operation
        
    Returns:
        int: Safe number of workers (0 on Windows, requested on Unix)
    """
    if force_single_thread is True:
        return 0
    
    if is_windows():
        if requested_workers > 0:
            warnings.warn(
                "Windows detected: Setting num_workers=0 to avoid multiprocessing issues. "
                "This may slow down data loading but ensures stability.",
                UserWarning
            )
        return 0
    
    return requested_workers

def get_safe_dataloader_kwargs(batch_size, shuffle=True, num_workers=4, 
                              pin_memory=None, persistent_workers=None,
                              prefetch_factor=2, drop_last=False, **kwargs):
    """
    Get Windows-safe DataLoader keyword arguments.
    
    Args:
        batch_size (int): Batch size
        shuffle (bool): Whether to shuffle data
        num_workers (int): Requested number of workers
        pin_memory (bool): Whether to use pinned memory (auto-detected if None)
        persistent_workers (bool): Whether to use persistent workers (auto-detected if None)
        prefetch_factor (int): Prefetch factor for data loading
        drop_last (bool): Whether to drop last incomplete batch
        **kwargs: Additional DataLoader arguments
        
    Returns:
        dict: Safe DataLoader keyword arguments
    """
    safe_kwargs = {
        'batch_size': batch_size,
        'shuffle': shuffle,
        'drop_last': drop_last,
        **kwargs  # Include any additional arguments
    }
    
    # Determine safe number of workers
    safe_num_workers = get_safe_num_workers(num_workers)
    safe_kwargs['num_workers'] = safe_num_workers
    
    # Configure memory pinning
    if pin_memory is None:
        # Auto-detect: use pin_memory only on CUDA and non-Windows
        pin_memory = torch.cuda.is_available() and not is_windows()
    safe_kwargs['pin_memory'] = pin_memory and not is_windows()
    
    # Configure persistent workers
    if persistent_workers is None:
        # Auto-detect: use persistent workers only with multiprocessing
        persistent_workers = safe_num_workers > 0
    
    # Only add persistent_workers if num_workers > 0 (PyTorch requirement)
    if safe_num_workers > 0:
        safe_kwargs['persistent_workers'] = persistent_workers
        
        # Only add prefetch_factor if num_workers > 0
        if prefetch_factor is not None:
            safe_kwargs['prefetch_factor'] = prefetch_factor
    
    return safe_kwargs

def create_safe_dataloader(dataset, batch_size, shuffle=True, num_workers=4,
                          pin_memory=None, persistent_workers=None,
                          prefetch_factor=2, drop_last=False, **kwargs):
    """
    Create a Windows-safe DataLoader.
    
    Args:
        dataset: PyTorch dataset
        batch_size (int): Batch size
        shuffle (bool): Whether to shuffle data
        num_workers (int): Requested number of workers
        pin_memory (bool): Whether to use pinned memory (auto-detected if None)
        persistent_workers (bool): Whether to use persistent workers (auto-detected if None)
        prefetch_factor (int): Prefetch factor for data loading
        drop_last (bool): Whether to drop last incomplete batch
        **kwargs: Additional DataLoader arguments
        
    Returns:
        DataLoader: Configured DataLoader instance
    """
    safe_kwargs = get_safe_dataloader_kwargs(
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=pin_memory,
        persistent_workers=persistent_workers,
        prefetch_factor=prefetch_factor,
        drop_last=drop_last,
        **kwargs
    )
    
    return DataLoader(dataset, **safe_kwargs)

def setup_multiprocessing():
    """
    Setup multiprocessing for Windows compatibility.
    This should be called at the beginning of training scripts.
    """
    if is_windows():
        # Set multiprocessing start method to 'spawn' on Windows
        try:
            torch.multiprocessing.set_start_method('spawn', force=True)
        except RuntimeError:
            # Start method already set
            pass
        
        # Disable multiprocessing warnings
        warnings.filterwarnings('ignore', category=UserWarning, 
                              message='.*multiprocessing.*')

def print_platform_info():
    """Print platform and compatibility information."""
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Python: {platform.python_version()}")
    print(f"PyTorch: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    
    if is_windows():
        print("Windows compatibility mode: Single-threaded data loading")
    else:
        print("Unix-like system: Multiprocessing data loading available")
    
    print("-" * 60)

def configure_torch_for_windows():
    """Configure PyTorch settings for optimal Windows performance."""
    if is_windows():
        # Optimize thread count for Windows
        num_threads = min(4, os.cpu_count())
        torch.set_num_threads(num_threads)
        
        # Disable CUDA optimizations that may cause issues on Windows
        if torch.cuda.is_available():
            torch.backends.cudnn.benchmark = False  # More conservative on Windows
            torch.backends.cudnn.deterministic = True
    else:
        # Standard optimizations for Unix-like systems
        torch.set_num_threads(min(8, os.cpu_count()))
        if torch.cuda.is_available():
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False

class WindowsSafeDataLoader:
    """
    A wrapper class that automatically creates Windows-safe DataLoaders.
    """
    
    @staticmethod
    def create_train_loader(dataset, batch_size, **kwargs):
        """Create a training DataLoader with Windows-safe settings."""
        return create_safe_dataloader(
            dataset=dataset,
            batch_size=batch_size,
            shuffle=True,
            drop_last=True,
            **kwargs
        )
    
    @staticmethod
    def create_val_loader(dataset, batch_size, **kwargs):
        """Create a validation DataLoader with Windows-safe settings."""
        return create_safe_dataloader(
            dataset=dataset,
            batch_size=batch_size,
            shuffle=False,
            drop_last=False,
            **kwargs
        )
    
    @staticmethod
    def create_test_loader(dataset, batch_size, **kwargs):
        """Create a test DataLoader with Windows-safe settings."""
        return create_safe_dataloader(
            dataset=dataset,
            batch_size=batch_size,
            shuffle=False,
            drop_last=False,
            **kwargs
        )

# Convenience functions for backward compatibility
def safe_dataloader(*args, **kwargs):
    """Alias for create_safe_dataloader."""
    return create_safe_dataloader(*args, **kwargs)

def windows_safe_workers(num_workers):
    """Alias for get_safe_num_workers."""
    return get_safe_num_workers(num_workers)

# Auto-setup when module is imported
if __name__ != "__main__":
    setup_multiprocessing()

# Example usage and testing
if __name__ == "__main__":
    print("Windows Compatibility Utilities for PyTorch")
    print("=" * 50)
    print_platform_info()
    
    # Test safe worker configuration
    print(f"Safe workers for 4 requested: {get_safe_num_workers(4)}")
    print(f"Safe workers for 0 requested: {get_safe_num_workers(0)}")
    
    # Test DataLoader kwargs
    kwargs = get_safe_dataloader_kwargs(batch_size=32, num_workers=4)
    print(f"Safe DataLoader kwargs: {kwargs}")
    
    print("\nConfiguration complete!")
