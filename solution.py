import os
import math
import torch
import torch.nn as nn
import torch.nn.functional as F

# -----------------------------------------------------------------------------
# Positional Encoding for transformer blocks (batch_first)
# -----------------------------------------------------------------------------
class PositionalEncoding(nn.Module):
    """Sinusoidal positional encoding (no learnable params)."""

    def __init__(self, d_model: int, max_len: int = 4096):
        super().__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float32).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2, dtype=torch.float32) * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer("pe", pe.unsqueeze(0))  # shape (1, max_len, d_model)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Args: x shape (B, L, d_model)"""
        if x.size(1) > self.pe.size(1):
            raise ValueError("Sequence length exceeds maximum positional encoding length")
        return x + self.pe[:, : x.size(1)]


# -----------------------------------------------------------------------------
# Main Model
# -----------------------------------------------------------------------------
class SVDNet(nn.Module):
    """CNN → Transformer backbone that predicts approximate SVD components.

    Constraints:
        • No call to torch.linalg.svd/qr/eig.
        • Orthogonality enforced via explicit Gram–Schmidt (differentiable).
    """

    def __init__(self, dim: int = 64, rank: int = 32, weight_path: str = "svdnet.pth"):
        super().__init__()
        self.dim = dim  # Antenna dimension (M == N)
        self.rank = rank

        # --------------------------- CNN Encoder ----------------------------
        # Deeper residual encoder with BatchNorm for faster convergence
        self.encoder = nn.Sequential(
            ConvBNReLU(2, 64, kernel_size=7, padding=3),
            ResBlock(64),
            nn.MaxPool2d(2),  # /2 spatial
            ResBlock(64),
            ConvBNReLU(64, 128),
            ResBlock(128),
            nn.MaxPool2d(2),  # /4 spatial (e.g., 64→16)
            ResBlock(128),
            ConvBNReLU(128, 256),
            ResBlock(256),
        )
        self.flatten_dim = 256 * 16 * 16  # C · H' · W'

        # ------------------------ Transformer Encoder -----------------------
        self.d_model = 256
        # Deeper transformer stack for richer global modelling
        encoder_layer = nn.TransformerEncoderLayer(d_model=self.d_model, nhead=8, batch_first=True)
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=4)
        self.pos_enc = PositionalEncoding(self.d_model, max_len=16 * 16)

        # ----------------------------- Heads --------------------------------
        self.fc_global = nn.Linear(self.d_model, 512)
        self.fc_U = nn.Linear(512, dim * rank * 2)
        self.fc_V = nn.Linear(512, dim * rank * 2)
        self.fc_S = nn.Linear(512, rank)

        # ------------------------ Weight loading ----------------------------
        if os.path.isfile(weight_path):
            state = torch.load(weight_path, map_location="cpu")
            try:
                self.load_state_dict(state, strict=False)
                print(f"[SVDNet] Loaded weights from {weight_path} (strict=False)")
            except RuntimeError as e:
                print(f"[SVDNet] Weight loading failed: {e}. Proceeding with random init.")

    # ---------------------------------------------------------------------
    def forward(self, x: torch.Tensor):
        """Args: x – complex channel, shape [M, N, 2] or [B, M, N, 2]"""
        if x.ndim == 3:
            x = x.unsqueeze(0)
        if x.shape[-1] != 2:
            raise ValueError("Input last dim must be 2 (real/imag)")
        B = x.size(0)

        # Reorder to NCHW
        feat = x.permute(0, 3, 1, 2)  # [B, 2, M, N]
        feat = self.encoder(feat)      # [B, 256, 16, 16]

        # Ensure a fixed 16×16 spatial size irrespective of original antenna dims
        feat = F.adaptive_avg_pool2d(feat, (16, 16))

        # Flatten into sequence for transformer: L = 256 (16*16)
        feat = feat.flatten(2).transpose(1, 2)  # [B, L, C] where C=256
        feat = self.pos_enc(feat)
        feat = self.transformer(feat)            # [B, L, C]
        feat = feat.mean(dim=1)                 # global average [B, C]

        # Global fc mapping
        g = F.relu(self.fc_global(feat))         # [B, 512]

        # Raw outputs (not yet orthonormal / positive)
        U_raw = self.fc_U(g).view(B, self.dim, self.rank, 2)
        V_raw = self.fc_V(g).view(B, self.dim, self.rank, 2)
        S_raw = self.fc_S(g)                     # [B, rank]

        # Orthonormalize U,V using differentiable Gram–Schmidt
        U = self._orthonormalize(U_raw)  # [B, dim, rank, 2]
        V = self._orthonormalize(V_raw)

        # Ensure singular values are non-negative and reasonably scaled
        S = F.softplus(S_raw)  # positive
        # Optionally normalise, but leave to training to learn scaling

        # Remove batch dim if B==1 (to match demo expectations)
        if B == 1:
            U = U.squeeze(0)
            V = V.squeeze(0)
            S = S.squeeze(0)
        return U, S, V

    # ------------------------------------------------------------------
    @staticmethod
    def _to_complex(mat: torch.Tensor) -> torch.Tensor:
        """Convert real-imag stacked tensor [..., 2] → complex."""
        return torch.complex(mat[..., 0], mat[..., 1])

    @staticmethod
    def _to_ri(mat: torch.Tensor) -> torch.Tensor:
        """Convert complex tensor → real-imag stacked."""
        return torch.stack((mat.real, mat.imag), dim=-1)

    def _orthonormalize(self, mat_ri: torch.Tensor) -> torch.Tensor:
        """Differentiable modified Gram–Schmidt producing orthonormal columns.
        Args:
            mat_ri: [B, dim, rank, 2]
        Returns: same shape with orthonormal columns (unitary truncated).
        """
        B, dim, rank, _ = mat_ri.shape
        mat_c = self._to_complex(mat_ri)  # [B, dim, rank]

        # Accumulate orthonormal columns without in-place modification (autograd safe)
        q_cols = []  # list of tensors shape [B, dim]
        for i in range(rank):
            v = mat_c[:, :, i]  # [B, dim]
            if i > 0:
                q_stack = torch.stack(q_cols, dim=2)  # [B, dim, i]
                r = torch.einsum("bdi,bd->bi", torch.conj(q_stack), v)  # projections
                v = v - torch.sum(q_stack * r.unsqueeze(1), dim=2)
            v_norm = torch.linalg.norm(v, dim=1, keepdim=True) + 1e-8
            v = v / v_norm
            q_cols.append(v)

        q_tensor = torch.stack(q_cols, dim=2)  # [B, dim, rank]
        return self._to_ri(q_tensor)

# ----------------------------- Residual Blocks -----------------------------
class ConvBNReLU(nn.Sequential):
    """Convolution + BatchNorm + ReLU helper."""
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3, stride: int = 1, padding: int = 1):
        super().__init__(
            nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
        )


class SEBlock(nn.Module):
    """Squeeze-and-Excitation block (channel attention)."""

    def __init__(self, channels: int, reduction: int = 16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid(),
        )

    def forward(self, x: torch.Tensor):  # type: ignore[override]
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y


class ResBlock(nn.Module):
    """Residual block with SE attention."""

    def __init__(self, channels: int):
        super().__init__()
        self.conv1 = ConvBNReLU(channels, channels)
        self.conv2 = ConvBNReLU(channels, channels)
        self.se = SEBlock(channels)

    def forward(self, x: torch.Tensor) -> torch.Tensor:  # type: ignore[override]
        out = self.conv2(self.conv1(x))
        out = self.se(out)
        return x + out