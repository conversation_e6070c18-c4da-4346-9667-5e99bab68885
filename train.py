import os
import glob
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from solution import SVDNet
import argparse
from tqdm import tqdm

# ----------------------------------------------------------------------------
# Utility functions
# ----------------------------------------------------------------------------

def read_cfg_file(file_path: str):
    """Parse the cfg txt to obtain antenna dims, IQ channels and rank.
    Returns: (samp_num, M, N, IQ, R)
    """
    with open(file_path, "r") as f:
        lines = [line.strip() for line in f.readlines() if line.strip()]
    samp_num = int(lines[0])
    M = int(lines[1])
    N = int(lines[2])
    IQ = int(lines[3])
    R = int(lines[4])
    return samp_num, M, N, IQ, R

# ----------------------------------------------------------------------------
# Dataset definition (memory-mapped to keep RAM usage low)
# ----------------------------------------------------------------------------

class ChannelDataset(Dataset):
    def __init__(self, data_files, label_files):
        assert len(data_files) == len(label_files), "data/label file count mismatch"
        self.data_arrays = [np.load(f, mmap_mode="r") for f in data_files]
        self.label_arrays = [np.load(f, mmap_mode="r") for f in label_files]
        self.cumsum = np.cumsum([arr.shape[0] for arr in self.data_arrays])

    def __len__(self):
        return int(self.cumsum[-1])

    def __getitem__(self, idx):
        file_idx = int(np.searchsorted(self.cumsum, idx, side="right"))
        prev_cum = 0 if file_idx == 0 else self.cumsum[file_idx - 1]
        inner_idx = idx - prev_cum
        H_in = self.data_arrays[file_idx][inner_idx]     # shape [M, N, 2]
        H_gt = self.label_arrays[file_idx][inner_idx]    # ideal channel
        # numpy -> torch
        H_in = torch.from_numpy(H_in).float()
        H_gt = torch.from_numpy(H_gt).float()
        return H_in, H_gt

# ----------------------------------------------------------------------------
# Complex helpers
# ----------------------------------------------------------------------------

def to_complex(t):
    return torch.complex(t[..., 0], t[..., 1])

# ----------------------------------------------------------------------------
# Training loop
# ----------------------------------------------------------------------------

def frob_norm(t):
    """Compute Frobenius norm squared for complex tensor."""
    return torch.sum(torch.abs(t) ** 2, dim=(-2, -1))  # returns [...]


def train(model, dataloader, device="cpu", lr=2e-4, epochs=20, lambda_orth=1e-2, weight_decay=1e-4):
    model.to(device)
    # AdamW with weight decay generally yields better generalisation
    opt = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    # Cosine annealing schedule encourages larger initial learning rate with gradual reduction
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(opt, T_0=5, T_mult=2)
    for ep in range(1, epochs + 1):
        model.train()
        total_loss = 0.0
        prog_bar = tqdm(dataloader, desc=f"Epoch {ep}/{epochs}")
        for H_in, H_gt in prog_bar:
            H_in = H_in.to(device)
            H_gt = H_gt.to(device)
            U, S, V = model(H_in)  # shapes with batch

            # Ensure batch dim
            if U.ndim == 3:  # squeezed when batch==1
                U = U.unsqueeze(0)
                V = V.unsqueeze(0)
                S = S.unsqueeze(0)

            # ----- Reconstruction loss (AE ratio) -----
            Uc = to_complex(U)
            Vc = to_complex(V)
            Sc = S.type(torch.complex64)
            U_S = Uc * Sc.unsqueeze(1)          # diag multiply along rank dim
            H_pred = torch.matmul(U_S, torch.conj(Vc).transpose(-2, -1))  # [B, M, N]
            H_gt_c = to_complex(H_gt)

            diff = H_pred - H_gt_c
            num = frob_norm(diff)                # [B]
            denom = frob_norm(H_gt_c) + 1e-8     # [B]
            ae = torch.mean(num / denom)         # average batch ratio

            # ----- Orthogonality regularization -----
            def orth_loss(mat):
                matH = torch.conj(mat).transpose(-2, -1)
                eye = torch.eye(mat.size(-1), device=mat.device, dtype=mat.dtype)
                prod = torch.matmul(matH, mat) - eye  # [B, R, R]
                return torch.mean(torch.abs(prod) ** 2)

            o_loss = orth_loss(Uc) + orth_loss(Vc)
            loss = ae + lambda_orth * o_loss
            opt.zero_grad()
            loss.backward()
            opt.step()
            total_loss += loss.item() * H_in.size(0)
            prog_bar.set_postfix({"loss": f"{loss.item():.4f}"})
        avg_loss = total_loss / len(dataloader.dataset)
        scheduler.step(ep)
        print(f"[Summary] Epoch {ep}/{epochs} | Avg Loss={avg_loss:.6f} | LR={scheduler.optimizer.param_groups[0]['lr']:.2e}")
    return model

# ----------------------------------------------------------------------------
# Main entry
# ----------------------------------------------------------------------------

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train SVDNet on provided channel data")
    parser.add_argument("--data_dir", type=str, default="CompetitionData1", help="Directory containing Round1TrainData*.npy")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Training device (cuda/cpu)")
    parser.add_argument("--batch_size", type=int, default=32)
    parser.add_argument("--epochs", type=int, default=20)
    parser.add_argument("--lr", type=float, default=2e-4)
    parser.add_argument("--lambda_orth", type=float, default=0.1)
    parser.add_argument("--weight_decay", type=float, default=1e-4)
    args = parser.parse_args()

    # Detect training files (Round1)
    data_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainData*.npy")))
    label_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainLabel*.npy")))
    cfg_file = os.path.join(args.data_dir, "Round1CfgData1.txt")  # assume same dims for all
    if not data_files or not label_files:
        raise FileNotFoundError("Training *.npy files not found under given data_dir")

    _, M, N, IQ, R = read_cfg_file(cfg_file)
    print(f"Found {len(data_files)} training files, each dim=({M},{N}), R={R}, device={args.device}")

    ds = ChannelDataset(data_files, label_files)
    loader = DataLoader(ds, batch_size=args.batch_size, shuffle=True, num_workers=0)

    model = SVDNet(dim=M, rank=R)  # init fresh (weights will be overwritten after training)
    trained_model = train(model, loader, device=args.device, lr=args.lr, epochs=args.epochs, lambda_orth=args.lambda_orth, weight_decay=args.weight_decay)

    torch.save(trained_model.state_dict(), "svdnet.pth")
    print("Weights saved to svdnet.pth") 