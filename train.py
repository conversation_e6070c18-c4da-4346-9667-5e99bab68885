import os
import glob
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from solution import SVDNet
import argparse
from tqdm import tqdm
import random
import math
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts, OneCycleLR
import warnings
warnings.filterwarnings('ignore')

# ----------------------------------------------------------------------------
# Utility functions
# ----------------------------------------------------------------------------

def read_cfg_file(file_path: str):
    """Parse the cfg txt to obtain antenna dims, IQ channels and rank.
    Returns: (samp_num, M, N, IQ, R)
    """
    with open(file_path, "r") as f:
        lines = [line.strip() for line in f.readlines() if line.strip()]
    samp_num = int(lines[0])
    M = int(lines[1])
    N = int(lines[2])
    IQ = int(lines[3])
    R = int(lines[4])
    return samp_num, M, N, IQ, R

# ----------------------------------------------------------------------------
# Enhanced Data Augmentation Functions
# ----------------------------------------------------------------------------

def add_awgn_noise(H, snr_db_range=(10, 30)):
    """Add Additive White Gaussian Noise to simulate thermal noise."""
    snr_db = random.uniform(*snr_db_range)
    snr_linear = 10 ** (snr_db / 10)

    # Calculate signal power
    signal_power = torch.mean(torch.abs(torch.complex(H[..., 0], H[..., 1])) ** 2)
    noise_power = signal_power / snr_linear

    # Generate complex noise
    noise_real = torch.randn_like(H[..., 0]) * torch.sqrt(noise_power / 2)
    noise_imag = torch.randn_like(H[..., 1]) * torch.sqrt(noise_power / 2)

    H_noisy = H.clone()
    H_noisy[..., 0] += noise_real
    H_noisy[..., 1] += noise_imag

    return H_noisy

def add_phase_noise(H, phase_noise_std=0.1):
    """Add phase noise to simulate oscillator imperfections."""
    M, N, _ = H.shape
    # Generate correlated phase noise (Wiener process)
    phase_noise = torch.cumsum(torch.randn(M, N) * phase_noise_std, dim=0)
    phase_noise = torch.cumsum(phase_noise, dim=1)

    # Apply phase rotation
    cos_phase = torch.cos(phase_noise)
    sin_phase = torch.sin(phase_noise)

    H_rotated = H.clone()
    real_part = H[..., 0] * cos_phase - H[..., 1] * sin_phase
    imag_part = H[..., 0] * sin_phase + H[..., 1] * cos_phase

    H_rotated[..., 0] = real_part
    H_rotated[..., 1] = imag_part

    return H_rotated

def add_doppler_shift(H, max_doppler_hz=100, sample_rate=1e6):
    """Add Doppler shift to simulate user mobility."""
    M, N, _ = H.shape
    doppler_freq = random.uniform(-max_doppler_hz, max_doppler_hz)

    # Create time-varying phase shift
    time_samples = torch.arange(M * N, dtype=torch.float32).view(M, N)
    phase_shift = 2 * math.pi * doppler_freq * time_samples / sample_rate

    cos_shift = torch.cos(phase_shift)
    sin_shift = torch.sin(phase_shift)

    H_shifted = H.clone()
    real_part = H[..., 0] * cos_shift - H[..., 1] * sin_shift
    imag_part = H[..., 0] * sin_shift + H[..., 1] * cos_shift

    H_shifted[..., 0] = real_part
    H_shifted[..., 1] = imag_part

    return H_shifted

def apply_random_augmentation(H_gt, augmentation_prob=0.7):
    """Apply random combination of augmentations."""
    H_augmented = H_gt.clone()

    if random.random() < augmentation_prob:
        # Apply AWGN noise
        if random.random() < 0.8:
            H_augmented = add_awgn_noise(H_augmented)

        # Apply phase noise
        if random.random() < 0.5:
            H_augmented = add_phase_noise(H_augmented)

        # Apply Doppler shift
        if random.random() < 0.3:
            H_augmented = add_doppler_shift(H_augmented)

    return H_augmented

# ----------------------------------------------------------------------------
# Enhanced Dataset with Data Augmentation
# ----------------------------------------------------------------------------

class EnhancedChannelDataset(Dataset):
    def __init__(self, data_files, label_files, augmentation=True, augmentation_prob=0.7):
        assert len(data_files) == len(label_files), "data/label file count mismatch"
        self.data_arrays = [np.load(f, mmap_mode="r") for f in data_files]
        self.label_arrays = [np.load(f, mmap_mode="r") for f in label_files]
        self.cumsum = np.cumsum([arr.shape[0] for arr in self.data_arrays])
        self.augmentation = augmentation
        self.augmentation_prob = augmentation_prob

    def __len__(self):
        return int(self.cumsum[-1])

    def __getitem__(self, idx):
        file_idx = int(np.searchsorted(self.cumsum, idx, side="right"))
        prev_cum = 0 if file_idx == 0 else self.cumsum[file_idx - 1]
        inner_idx = idx - prev_cum
        H_in = self.data_arrays[file_idx][inner_idx]     # shape [M, N, 2]
        H_gt = self.label_arrays[file_idx][inner_idx]    # ideal channel

        # Convert to torch tensors
        H_in = torch.from_numpy(H_in).float()
        H_gt = torch.from_numpy(H_gt).float()

        # Apply data augmentation during training
        if self.augmentation:
            H_in = apply_random_augmentation(H_gt, self.augmentation_prob)

        return H_in, H_gt

# ----------------------------------------------------------------------------
# Enhanced Loss Functions and Training Utilities
# ----------------------------------------------------------------------------

def to_complex(t):
    return torch.complex(t[..., 0], t[..., 1])

def frob_norm(t):
    """Compute Frobenius norm squared for complex tensor."""
    return torch.sum(torch.abs(t) ** 2, dim=(-2, -1))

class AdaptiveLossWeighting(nn.Module):
    """Adaptive loss weighting based on uncertainty estimation."""

    def __init__(self, num_tasks=3):
        super().__init__()
        self.log_vars = nn.Parameter(torch.zeros(num_tasks))

    def forward(self, losses):
        """
        Args:
            losses: List of loss values [reconstruction_loss, orth_loss_U, orth_loss_V]
        """
        weighted_losses = []
        for i, loss in enumerate(losses):
            precision = torch.exp(-self.log_vars[i])
            weighted_loss = precision * loss + self.log_vars[i]
            weighted_losses.append(weighted_loss)

        return sum(weighted_losses), weighted_losses

def compute_enhanced_losses(U, S, V, H_gt, device):
    """Compute enhanced loss components with better numerical stability."""
    # Ensure batch dimension
    if U.ndim == 3:
        U = U.unsqueeze(0)
        V = V.unsqueeze(0)
        S = S.unsqueeze(0)

    # Convert to complex
    Uc = to_complex(U)
    Vc = to_complex(V)
    H_gt_c = to_complex(H_gt)

    # Enhanced reconstruction loss with multiple metrics
    U_S = Uc * S.unsqueeze(1).type(torch.complex64)
    H_pred = torch.matmul(U_S, torch.conj(Vc).transpose(-2, -1))

    # Primary reconstruction loss (relative Frobenius norm)
    diff = H_pred - H_gt_c
    num = frob_norm(diff)
    denom = frob_norm(H_gt_c) + 1e-8
    reconstruction_loss = torch.mean(num / denom)

    # Additional spectral loss (singular value preservation)
    H_gt_svd_approx = torch.linalg.svd(H_gt_c, full_matrices=False)
    gt_singular_values = H_gt_svd_approx.S[:, :S.size(-1)]  # Take first R singular values
    spectral_loss = torch.mean(torch.abs(S - gt_singular_values) ** 2)

    # Enhanced orthogonality losses
    def enhanced_orth_loss(mat):
        matH = torch.conj(mat).transpose(-2, -1)
        eye = torch.eye(mat.size(-1), device=device, dtype=mat.dtype).unsqueeze(0).expand(mat.size(0), -1, -1)
        prod = torch.matmul(matH, mat) - eye
        return torch.mean(torch.abs(prod) ** 2)

    orth_loss_U = enhanced_orth_loss(Uc)
    orth_loss_V = enhanced_orth_loss(Vc)

    return reconstruction_loss, spectral_loss, orth_loss_U, orth_loss_V

def train_enhanced(model, dataloader, val_dataloader=None, device="cpu", lr=1e-3, epochs=50,
                  use_adaptive_weighting=True, gradient_clip_val=1.0, warmup_epochs=5):
    """Enhanced training loop with advanced optimization techniques."""
    model.to(device)

    # Enhanced optimizer with better hyperparameters
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=lr,
        weight_decay=1e-4,
        betas=(0.9, 0.999),
        eps=1e-8
    )

    # Advanced learning rate scheduling
    total_steps = len(dataloader) * epochs
    scheduler = OneCycleLR(
        optimizer,
        max_lr=lr,
        total_steps=total_steps,
        pct_start=0.1,  # 10% warmup
        anneal_strategy='cos',
        div_factor=25.0,
        final_div_factor=1e4
    )

    # Adaptive loss weighting
    if use_adaptive_weighting:
        loss_weighter = AdaptiveLossWeighting(num_tasks=4).to(device)
        loss_optimizer = torch.optim.Adam(loss_weighter.parameters(), lr=1e-3)

    best_val_loss = float('inf')
    patience = 10
    patience_counter = 0

    for epoch in range(1, epochs + 1):
        model.train()
        total_loss = 0.0
        total_recon_loss = 0.0
        total_spectral_loss = 0.0
        total_orth_loss = 0.0

        prog_bar = tqdm(dataloader, desc=f"Epoch {epoch}/{epochs}")

        for batch_idx, (H_in, H_gt) in enumerate(prog_bar):
            H_in = H_in.to(device)
            H_gt = H_gt.to(device)

            # Forward pass
            U, S, V = model(H_in)

            # Compute enhanced losses
            recon_loss, spectral_loss, orth_loss_U, orth_loss_V = compute_enhanced_losses(
                U, S, V, H_gt, device
            )

            # Combine losses
            if use_adaptive_weighting:
                losses = [recon_loss, spectral_loss, orth_loss_U, orth_loss_V]
                total_weighted_loss, individual_losses = loss_weighter(losses)
                loss = total_weighted_loss
            else:
                loss = recon_loss + 0.1 * spectral_loss + 0.01 * (orth_loss_U + orth_loss_V)

            # Backward pass with gradient clipping
            optimizer.zero_grad()
            if use_adaptive_weighting:
                loss_optimizer.zero_grad()

            loss.backward()

            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(model.parameters(), gradient_clip_val)

            optimizer.step()
            if use_adaptive_weighting:
                loss_optimizer.step()

            scheduler.step()

            # Update statistics
            total_loss += loss.item()
            total_recon_loss += recon_loss.item()
            total_spectral_loss += spectral_loss.item()
            total_orth_loss += (orth_loss_U.item() + orth_loss_V.item())

            # Update progress bar
            current_lr = scheduler.get_last_lr()[0]
            prog_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'recon': f'{recon_loss.item():.4f}',
                'lr': f'{current_lr:.2e}'
            })

        # Epoch summary
        avg_loss = total_loss / len(dataloader)
        avg_recon = total_recon_loss / len(dataloader)
        avg_spectral = total_spectral_loss / len(dataloader)
        avg_orth = total_orth_loss / len(dataloader)

        print(f"[Epoch {epoch}] Loss: {avg_loss:.6f} | Recon: {avg_recon:.6f} | "
              f"Spectral: {avg_spectral:.6f} | Orth: {avg_orth:.6f}")

        # Validation
        if val_dataloader is not None:
            val_loss = validate_model(model, val_dataloader, device)
            print(f"[Validation] Loss: {val_loss:.6f}")

            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # Save best model
                torch.save(model.state_dict(), "best_svdnet.pth")
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"Early stopping at epoch {epoch}")
                    break

    return model

def validate_model(model, val_dataloader, device):
    """Validation function."""
    model.eval()
    total_loss = 0.0

    with torch.no_grad():
        for H_in, H_gt in val_dataloader:
            H_in = H_in.to(device)
            H_gt = H_gt.to(device)

            U, S, V = model(H_in)
            recon_loss, spectral_loss, orth_loss_U, orth_loss_V = compute_enhanced_losses(
                U, S, V, H_gt, device
            )

            loss = recon_loss + 0.1 * spectral_loss + 0.01 * (orth_loss_U + orth_loss_V)
            total_loss += loss.item()

    return total_loss / len(val_dataloader)

# ----------------------------------------------------------------------------
# Enhanced Main Entry with Advanced Training Pipeline
# ----------------------------------------------------------------------------

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Enhanced SVDNet Training with Advanced Optimization")
    parser.add_argument("--data_dir", type=str, default="CompetitionData1",
                       help="Directory containing Round1TrainData*.npy")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu",
                       help="Training device (cuda/cpu)")
    parser.add_argument("--batch_size", type=int, default=16,
                       help="Batch size (reduced for larger model)")
    parser.add_argument("--epochs", type=int, default=100,
                       help="Number of training epochs")
    parser.add_argument("--lr", type=float, default=1e-3,
                       help="Peak learning rate for OneCycleLR")
    parser.add_argument("--val_split", type=float, default=0.1,
                       help="Validation split ratio")
    parser.add_argument("--augmentation", action="store_true", default=True,
                       help="Enable data augmentation")
    parser.add_argument("--adaptive_weighting", action="store_true", default=True,
                       help="Use adaptive loss weighting")
    parser.add_argument("--gradient_clip", type=float, default=1.0,
                       help="Gradient clipping value")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed for reproducibility")
    args = parser.parse_args()

    # Set random seeds for reproducibility
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    random.seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)
        torch.cuda.manual_seed_all(args.seed)

    # Detect training files
    data_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainData*.npy")))
    label_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainLabel*.npy")))
    cfg_file = os.path.join(args.data_dir, "Round1CfgData1.txt")

    if not data_files or not label_files:
        raise FileNotFoundError("Training *.npy files not found under given data_dir")

    _, M, N, IQ, R = read_cfg_file(cfg_file)
    print(f"Configuration: M={M}, N={N}, IQ={IQ}, R={R}")
    print(f"Found {len(data_files)} training files, device={args.device}")
    print(f"Enhanced training with augmentation={args.augmentation}, adaptive_weighting={args.adaptive_weighting}")

    # Create enhanced dataset with augmentation
    full_dataset = EnhancedChannelDataset(
        data_files, label_files,
        augmentation=args.augmentation,
        augmentation_prob=0.8
    )

    # Split dataset into train and validation
    dataset_size = len(full_dataset)
    val_size = int(args.val_split * dataset_size)
    train_size = dataset_size - val_size

    train_dataset, val_dataset = torch.utils.data.random_split(
        full_dataset, [train_size, val_size],
        generator=torch.Generator().manual_seed(args.seed)
    )

    # Create validation dataset without augmentation
    val_dataset.dataset.augmentation = False

    # Create data loaders with optimized settings
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4 if args.device == 'cuda' else 0,
        pin_memory=True if args.device == 'cuda' else False,
        persistent_workers=True if args.device == 'cuda' else False
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size * 2,  # Larger batch for validation
        shuffle=False,
        num_workers=2 if args.device == 'cuda' else 0,
        pin_memory=True if args.device == 'cuda' else False
    )

    print(f"Training samples: {len(train_dataset)}, Validation samples: {len(val_dataset)}")

    # Initialize enhanced model
    model = SVDNet(dim=M, rank=R, weight_path="")  # Don't load weights initially

    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")

    # Enhanced training
    print("Starting enhanced training...")
    trained_model = train_enhanced(
        model,
        train_loader,
        val_dataloader=val_loader,
        device=args.device,
        lr=args.lr,
        epochs=args.epochs,
        use_adaptive_weighting=args.adaptive_weighting,
        gradient_clip_val=args.gradient_clip
    )

    # Save final model
    torch.save(trained_model.state_dict(), "svdnet.pth")
    print("Enhanced training completed! Weights saved to svdnet.pth")

    # Load and save best model if exists
    if os.path.exists("best_svdnet.pth"):
        print("Best model saved as best_svdnet.pth")
        # Copy best model as the final model
        import shutil
        shutil.copy("best_svdnet.pth", "svdnet.pth")
        print("Best model copied to svdnet.pth")